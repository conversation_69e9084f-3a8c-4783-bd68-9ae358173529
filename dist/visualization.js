// Main visualization module for Dformer training curves
// Uses ECharts for academic-quality plotting
import { loadModelDatasets, filterDataByEpochRange, filterModelsByType, filterModelsByFramework } from './data_loader.js';
class DformerVisualization {
    constructor(containerId) {
        this.currentData = [];
        this.chartContainer = document.getElementById(containerId);
        this.chart = echarts.init(this.chartContainer, null, {
            renderer: 'canvas',
            useDirtyRect: false
        });
        this.setupEventListeners();
        this.loadDataAndInitialize();
        // Handle window resize
        window.addEventListener('resize', () => {
            this.chart.resize();
        });
    }
    async loadDataAndInitialize() {
        try {
            this.currentData = await loadModelDatasets();
            this.initializeChart();
        }
        catch (error) {
            console.error('Failed to load training data:', error);
        }
    }
    setupEventListeners() {
        // Model filter
        const modelFilter = document.getElementById('modelFilter');
        modelFilter.addEventListener('change', () => {
            this.updateChart();
        });
        // Framework filter
        const frameworkFilter = document.getElementById('frameworkFilter');
        frameworkFilter.addEventListener('change', () => {
            this.updateChart();
        });
        // Epoch range filter
        const epochRange = document.getElementById('epochRange');
        epochRange.addEventListener('change', () => {
            this.updateChart();
        });
        // Reset zoom button
        const resetZoom = document.getElementById('resetZoom');
        resetZoom.addEventListener('click', () => {
            this.chart.dispatchAction({
                type: 'dataZoom',
                start: 0,
                end: 100
            });
        });
        // Download button
        const downloadBtn = document.getElementById('downloadChart');
        downloadBtn.addEventListener('click', () => {
            this.downloadChart();
        });
    }
    getFilteredData() {
        const modelFilter = document.getElementById('modelFilter').value;
        const frameworkFilter = document.getElementById('frameworkFilter').value;
        const epochRange = document.getElementById('epochRange').value;
        let filteredData = filterModelsByType(this.currentData, modelFilter);
        filteredData = filterModelsByFramework(filteredData, frameworkFilter);
        // Apply epoch range filter to data
        return filteredData.map(model => ({
            ...model,
            data: filterDataByEpochRange(model.data, epochRange)
        }));
    }
    generateEChartsOption() {
        const filteredData = this.getFilteredData();
        // Prepare series data
        const series = filteredData.map(model => ({
            name: model.name,
            type: 'line',
            data: model.data.map(point => [point.epoch, point.mIoU]),
            smooth: 0.2, // Reduced smoothing to show more fluctuations
            lineStyle: {
                color: model.color,
                width: model.lineType === 'solid' ? 2.5 : 2.5,
                type: model.lineType === 'dashed' ? 'dashed' : 'solid'
            },
            itemStyle: {
                color: model.color
            },
            symbol: 'none', // Hide symbols to show fluctuations better
            symbolSize: 3,
            showSymbol: false, // Only show symbols on hover
            emphasis: {
                focus: 'series',
                lineStyle: {
                    width: 3.5
                }
            },
            markPoint: model.framework === 'pytorch' ? {
                data: [
                    {
                        type: 'max',
                        name: 'Peak Performance',
                        symbol: 'pin',
                        symbolSize: 50,
                        itemStyle: {
                            color: model.color
                        }
                    }
                ]
            } : undefined
        }));
        return {
            title: {
                text: 'Dformer Model Training Convergence Curves',
                subtext: 'mIoU Performance Comparison on NYUDepthv2 Dataset',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold',
                    fontFamily: 'Times New Roman'
                },
                subtextStyle: {
                    fontSize: 14,
                    fontFamily: 'Times New Roman'
                }
            },
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                borderColor: '#ccc',
                borderWidth: 1,
                textStyle: {
                    color: '#333',
                    fontFamily: 'Times New Roman'
                },
                formatter: function (params) {
                    let content = `<strong>Epoch ${params[0].axisValue}</strong><br/>`;
                    params.forEach(param => {
                        const framework = param.seriesName.includes('PyTorch') ? 'PyTorch' : 'Jittor';
                        content += `<span style="color:${param.color}">●</span> ${param.seriesName}: <strong>${param.value[1].toFixed(2)}%</strong><br/>`;
                    });
                    return content;
                }
            },
            legend: {
                type: 'scroll',
                orient: 'horizontal',
                left: 'center',
                top: 60,
                textStyle: {
                    fontSize: 12,
                    fontFamily: 'Times New Roman'
                },
                pageButtonItemGap: 10,
                pageButtonGap: 20,
                pageButtonPosition: 'end',
                animation: true
            },
            grid: {
                left: '8%',
                right: '4%',
                bottom: '15%',
                top: '20%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                name: 'Training Epoch',
                nameLocation: 'center',
                nameGap: 35,
                nameTextStyle: {
                    fontSize: 14,
                    fontWeight: 'bold',
                    fontFamily: 'Times New Roman'
                },
                axisLabel: {
                    fontSize: 12,
                    fontFamily: 'Times New Roman'
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        type: 'dashed',
                        opacity: 0.3
                    }
                },
                axisLine: {
                    lineStyle: {
                        width: 1.5
                    }
                }
            },
            yAxis: {
                type: 'value',
                name: 'mIoU (%)',
                nameLocation: 'center',
                nameGap: 40,
                nameTextStyle: {
                    fontSize: 14,
                    fontWeight: 'bold',
                    fontFamily: 'Times New Roman'
                },
                axisLabel: {
                    fontSize: 12,
                    fontFamily: 'Times New Roman',
                    formatter: '{value}%'
                },
                min: function (value) {
                    return Math.max(0, value.min - 5);
                },
                max: function (value) {
                    return Math.min(75, value.max + 2);
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        type: 'dashed',
                        opacity: 0.3
                    }
                },
                axisLine: {
                    lineStyle: {
                        width: 1.5
                    }
                }
            },
            series: series,
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    end: 100,
                    filterMode: 'filter'
                },
                {
                    type: 'slider',
                    start: 0,
                    end: 100,
                    height: 30,
                    bottom: 10,
                    textStyle: {
                        fontSize: 12,
                        fontFamily: 'Times New Roman'
                    }
                }
            ],
            toolbox: {
                feature: {
                    dataZoom: {
                        yAxisIndex: 'none'
                    },
                    restore: {},
                    saveAsImage: {
                        name: 'dformer_training_curves',
                        type: 'png',
                        backgroundColor: '#fff',
                        pixelRatio: 2
                    }
                },
                right: 20,
                top: 20
            },
            animation: true,
            animationDuration: 1000,
            animationEasing: 'cubicOut'
        };
    }
    initializeChart() {
        this.updateChart();
    }
    updateChart() {
        const option = this.generateEChartsOption();
        this.chart.setOption(option, true);
    }
    downloadChart() {
        const url = this.chart.getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: '#fff'
        });
        const link = document.createElement('a');
        link.download = 'dformer_training_curves.png';
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    resize() {
        this.chart.resize();
    }
    dispose() {
        this.chart.dispose();
    }
}
// Initialize visualization when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    const visualization = new DformerVisualization('trainingChart');
    // Make it globally accessible for debugging
    window.dformerViz = visualization;
    console.log('Dformer Training Visualization initialized successfully!');
    // Load and log data info
    try {
        const datasets = await loadModelDatasets();
        console.log('Available models:', datasets.length);
        console.log('Data points per model:', datasets[0]?.data.length || 0);
    }
    catch (error) {
        console.error('Failed to load model information:', error);
    }
});
export { DformerVisualization };
//# sourceMappingURL=visualization.js.map