export interface TrainingPoint {
    epoch: number;
    mIoU: number;
}
export interface ModelData {
    name: string;
    framework: 'pytorch' | 'jittor';
    color: string;
    lineType: 'solid' | 'dashed';
    data: TrainingPoint[];
}
export declare function loadModelDatasets(): Promise<ModelData[]>;
export declare function filterDataByEpochRange(data: TrainingPoint[], range: 'full' | 'early' | 'late'): TrainingPoint[];
export declare function filterModelsByType(models: ModelData[], filter: string): ModelData[];
export declare function filterModelsByFramework(models: ModelData[], framework: string): ModelData[];
